export default TimeDivider;
/**
 * The separator between the current time and duration.
 * Can be hidden if it's not needed in the design.
 *
 * @extends Component
 */
declare class TimeDivider extends Component {
    /**
     * Create the component's DOM element
     *
     * @return {Element}
     *         The element that was created.
     */
    createEl(): Element;
}
import Component from '../../component.js';
//# sourceMappingURL=time-divider.d.ts.map