// css for the old live ui, assumes that the progress bar is hidden
.video-js .vjs-live-control {
  @include display-flex(flex-start);
  @include flex(auto);
  font-size: 1em;
  line-height: 3em;
}

// hide the LiveDisplay when not live or when
// the new liveui is in use
.video-js:not(.vjs-live) .vjs-live-control,
.video-js.vjs-liveui .vjs-live-control {
  display: none;
}

// css for the new live ui below
.video-js .vjs-seek-to-live-control {
  align-items: center;
  cursor: pointer;
  @include flex(none);
  display: inline-flex;
  height: 100%;
  padding-left: 0.5em;
  padding-right: 0.5em;
  font-size: 1em;
  line-height: 3em;
  width: auto;
  min-width: 4em;
}

// hide the SeekToLive button when not live and
// when the liveui is not in use
.video-js.vjs-live:not(.vjs-liveui) .vjs-seek-to-live-control,
.video-js:not(.vjs-live) .vjs-seek-to-live-control {
  display: none;
}

// only show as a pointer when we will seek to live edge
.vjs-seek-to-live-control.vjs-control.vjs-at-live-edge {
  cursor: auto;
}

.vjs-seek-to-live-control .vjs-icon-placeholder {
  margin-right: 0.5em;
  @extend .vjs-icon-circle;
  color: #888;
}

.vjs-svg-icons-enabled .vjs-seek-to-live-control {
  line-height: 0;
}

.vjs-seek-to-live-control .vjs-svg-icon {
  width: 1em;
  height: 1em;
  pointer-events: none;
  fill: #888888;
}

// make the live circle red when at the live edge
.vjs-seek-to-live-control.vjs-control.vjs-at-live-edge .vjs-icon-placeholder {
  color: red;
}
.vjs-seek-to-live-control.vjs-control.vjs-at-live-edge .vjs-svg-icon {
  fill: red;
}
