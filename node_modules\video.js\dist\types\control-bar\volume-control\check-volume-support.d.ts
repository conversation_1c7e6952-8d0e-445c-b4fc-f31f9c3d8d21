export default checkVolumeSupport;
/** @import Component from '../../component' */
/** @import Player from '../../player' */
/**
 * Check if volume control is supported and if it isn't hide the
 * `Component` that was passed  using the `vjs-hidden` class.
 *
 * @param {Component} self
 *        The component that should be hidden if volume is unsupported
 *
 * @param {Player} player
 *        A reference to the player
 *
 * @private
 */
declare function checkVolumeSupport(self: Component, player: Player): void;
import type Component from '../../component';
import type Player from '../../player';
//# sourceMappingURL=check-volume-support.d.ts.map