{"version": 3, "file": "player.d.ts", "sourceRoot": "", "sources": ["../../src/js/player.js"], "names": [], "mappings": ";oDAoEc,IAAI;AAmOlB;;;;;;;;;;;GAWG;AACH;IA+0JE;;;;;;;;;OASG;IACH,2BAPW,OAAO,OAsDjB;IAt4JD;;;;;;;;;;;OAWG;IACH,iBATW,OAAO,yBAMP,mBAAmB,EAsT7B;IA5QC,iDAA8E;IAC9E,2CAAiE;IAEjE,sCAAwD;IACxD,sCAAwD;IACxD,gDAA4E;IAE5E,wCAA4D;IAC5D,8CAAwE;IACxE,6CAAsE;IACtE,4CAAoE;IACpE,2CAAkE;IAClE,sCAAwD;IAExD,0DAAgG;IAGhG,uBAA0B;IAG1B,SAAiC;IAGjC,YAA2B;IAG3B,2BAA8B;IAI9B,wBAA0B;IAM1B,qBAAwB;IAGxB,qBAAwB;IAGxB,uBAA0B;IAG1B,wBAA2B;IAG3B,0BAA6B;IAG7B;;;;MAIC;IAaD,aAAc;IAGd,mBAAkD;IAahD,eAAkC;IAQpC,mBAAmB;IACnB,SADU,MAAM,CACmB;IAGnC,sBAAsB;IACtB,WADW,OAAO,CACiB;IAQnC,sBAAyB;IACzB,sBAAwB;IACxB,4BAA8B;IA0B9B,oBAAuB;IAEvB,aAA0B;IAqC1B,mBAAqB;IAmDnB,qCAAoD;IA+CxD;;;;;;;OAOG;IACH,gBA+DC;IA9CG,kBAAoB;IAqBpB,qBAA2B;IA2B/B;;;;;OAKG;IACH,YAHY,OAAO,CAyKlB;IAjDC,eAAkB;IAClB,gBAAmB;IAkDrB;;;;;;;;;;;;;;OAcG;IACH,oBARW,MAAM,GAAC,IAAI,GAIV,MAAM,GAAC,IAAI,GAAC,SAAS,CAqBhC;IAED;;;;;;;;;;OAUG;IACH,cAPW,MAAM,GAAC,MAAM,GAGZ,MAAM,GAAC,SAAS,CAM3B;IAED;;;;;;;;;;OAUG;IACH,eAPW,MAAM,GAAC,MAAM,GAGZ,MAAM,GAAC,SAAS,CAM3B;IAED;;;;;;;;;;;;;OAaG;IACH,qBAXW,MAAM,UAKN,MAAM,GAAC,MAAM,GAGZ,MAAM,CA0BjB;IAED;;;;;;;;;;;;;OAaG;IACH,aATW,OAAO,GAKN,OAAO,GAAC,SAAS,CAyB5B;IAED;;;;;;;;;;;;;OAaG;IACH,YATW,OAAO,GAKN,OAAO,GAAC,SAAS,CAiB5B;IAED;;;;;;;;OAQG;IAEH;;;;;;;;;OASG;IACH,oBAPW,MAAM,GAGL,MAAM,GAAC,SAAS,CAoB3B;IAPC,qBAAyB;IAS3B;;;;;OAKG;IACH,uBA8EC;IAED;;;;;;;;;;;OAWG;IACH,kBAuIC;IAtHC,kBAA8B;IA0D9B,WAAuC;IA8DzC;;;;OAIG;IACH,oBAqBC;IAdC,uBAAsE;IAgBxE;;;;;;;;;;OAUG;IACH,cANW,GAAC,GAGA,IAAI,CAUf;IAED;;;;;;OAMG;IAEH;;;;;OAKG;IACH;;;;YATc,MAAM;MAanB;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,kCAiBC;IAED;;;;;OAKG;IACH,qCASC;IAED;;;;OAIG;IACH,yBAaC;IAED;;;;;;OAMG;IACH,6BA4BC;IAED;;;;;OAKG;IACH,0CAwDC;IAED;;;;;;;;;;OAUG;IACH,2CAkDC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACH;;;;;;OAMG;IACH,6BA+CC;IANC;;;MAAsE;IAQxE;;;;;;;;;;OAUG;IACH,oBAPW,OAAO,GAIN,OAAO,CAoBlB;IAED;;;;;;;OAOG;IACH,wBAcC;IAED;;;;;;;;;;OAUG;IACH,8BAaC;IAED;;;;;;OAMG;IACH,2BAqBC;IAED;;;;;;;OAOG;IACH,2BASC;IAED;;;;;;OAMG;IACH,kCAUC;IAED;;;;;;OAMG;IACH,2BASC;IAED;;;;;;OAMG;IACH,2BASC;IAED;;;;;;OAMG;IACH,0BASC;IAED;;;;;;OAMG;IACH,yBAUC;IAED;;;;;;OAMG;IACH,yBAiBC;IAED;;;;;OAKG;IACH,kCAEC;IAED;;;;;;;;OAQG;IACH,yBA4BC;IAED;;;;;;;;;OASG;IACH,+BA+CC;IAED;;;;;;OAMG;IACH,uBAEC;IAED;;;;;OAKG;IACH,8BAEC;IADC,uBAAsC;IAGxC;;;;;OAKG;IACH,6BAIC;IAED;;;;;;;;;OASG;IACH,4BAKC;IAED;;OAEG;IACH,+BAMC;IAED;;OAEG;IACH,wCAiBC;IAED;;;;;;;;;;;;OAYG;IACH,oCAUC;IAED,uDAEC;IAED;;OAEG;IACH,qCAMC;IAED;;;;;;;;OAQG;IACH,yCAEC;IAED;;;;;;;;OAQG;IACH,yCAEC;IAED;;;;;OAKG;IACH,yBAMC;IAED;;;;;;OAMG;IACH,4BAcC;IAED;;;;;OAKG;IACH,gBAEC;IAED;;;;;;;OAOG;IACH,oBAoBC;IAnBC;;;;;;;;;;;;;MAkBC;IAGH;;;;;;;;;;OAUG;IACH,kBAoBC;IAED;;;;;;;;;;;;OAYG;IACH,iBAmCC;IAED;;;;;;;;;OASG;IACH,QAPY,eAAQ,SAAS,CAW5B;IAED;;;;;;;;OAQG;IACH,cA2CC;IAlCG,8BAAuB;IAoC3B;;;;OAIG;IACH,gCAQC;IAED;;;;;;;;OAQG;IACH,uBAHW,SAAS,eAAQ,QAa3B;IAED;;OAEG;IACH,cAEC;IAED;;;;;;OAMG;IACH,UAJY,OAAO,CAOlB;IAED;;;;;;;OAOG;IACH,UAJY,SAAS,CAMpB;IAED;;;;;;;;;;;OAWG;IACH,wBAPW,OAAO,GAGN,OAAO,GAAC,SAAS,CAgB5B;IAED;;;;;;;;;OASG;IACH,sBAPW,MAAM,GAAC,MAAM,GAGZ,MAAM,GAAC,SAAS,CAiC3B;IAED;;;;OAIG;IACH,uBAEC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,mBAPW,MAAM,GAGL,MAAM,GAAC,SAAS,CAqC3B;IAED;;;;;;OAMG;IACH,iBAHY,MAAM,CAKjB;IAED;;;;;;OAMG;IACH,wBAHY,MAAM,CAKjB;IAKD;;;;;;;;;OASG;IACH,YAHY,SAAS,CAWpB;IAED;;;;;;;;OAQG;IACH,YAHY,SAAS,CAWpB;IAED;;;;OAIG;IACH,WAFY,OAAO,CAIlB;IAED;;;;OAIG;IACH,SAFY,OAAO,CAIlB;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACH,gBAFY,MAAM,CAIjB;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,cAFY,MAAM,CAIjB;IAED;;;;;;;OAOG;IACH,mBAJY,MAAM,CAMjB;IAED;;;;;;OAMG;IACH,eAHY,MAAM,CAajB;IAED;;;;;;;;;;;OAWG;IACH,0BATY,MAAM,GAMN,MAAM,GAAC,SAAS,CAsB3B;IAED;;;;;;;;;;;OAWG;IACH,cATW,OAAO,GAIN,OAAO,GAAC,SAAS,CAW5B;IAED;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACH,4BATW,OAAO,GAIN,OAAO,GAAC,SAAS,CAU5B;IAED;;;;;;;;;;;;;;OAcG;IACH,oBAMC;IAED;;;;;;OAMG;IACH,sBAHY,OAAO,CAKlB;IAED;;;;;;;;;;;;;;;OAeG;IACH,oBARY,OAAO,GAGP,OAAO,GAAC,SAAS,CA0B5B;IAED;;;;;;;;;;;;;OAaG;IACH,yDA+BC;IAED,sDAqCC;IAED;;;;OAIG;IACH,+BA4BC;IAED,6BAiBC;IAED;;;;;OAKG;IACH,wBAqBC;IAnBC,sBAAwB;IAGxB,qBAA8D;IAkBhE;;;;;;OAMG;IACH,0BAHW,MAAM,QAahB;IAED;;;;OAIG;IACH,uBAkBC;IAED;;;;;;OAMG;IACH,gCAJW,OAAO,OAWjB;IAED;;;;;;;;;;;OAWG;IACH,6BARY,OAAO,GAGP,OAAO,GAAC,SAAS,CAY5B;IALG,+BAAoC;IAOxC;;;;;;;;;;;;;;;;;;OAkBG;IACH,wCAkDC;IAED;;;;;;;;;OASG;IACH,qCAgBC;IAgED;;;;;;;;;;OAUG;IACH,qBAHW,KAAK,QAuCf;IAED;;;;;;;;;;OAUG;IACH,kBANW,MAAM,GAGL,MAAM,CAkCjB;IAED;;;;;;;;;;OAUG;IACH,8BAHY,MAAO,OAAO,CA4DzB;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,kCAPW,OAAO,GAGN,MAAM,GAAC,SAAS,CAgG3B;IALG,+BAGC;IAIL;;;;;;;;;;;;;;OAcG;IACH,kBAJY,MAAM,GAAC,SAAS,CAM3B;IAED;;;;;;;;;;;;OAYG;IACH,aAmCC;IAED;;;;;;;;;;;OAWG;IACH,yBATW,MAAM,aAGN,MAAM,GAGL,OAAO,CASlB;IAED;;;;;;;;OAQG;IACH,4BANW,MAAM,GAGL,OAAO,CASlB;IAED;;OAEG;IACH,aAUC;IAED;;;;OAIG;IACH,cAQC;IAED,iBA0BC;IAED;;;OAGG;IACH,2BAIC;IAED;;OAEG;IACH,0BA8BC;IAED;;OAEG;IACH,2BAGC;IAED;;OAEG;IACH,wBAGC;IAED;;;;;OAKG;IACH,kBAHY,IAAI,CAaf;IAED;;;;;OAKG;IACH,iBAHY,IAAI,CAKf;IAED;;;;;;OAMG;IACH,cAHY,MAAM,CAKjB;IAED;;;;;;;OAOG;IACH,eAHY,MAAM,CAKjB;IAED;;;;;;;;;OASG;IACH,gBAPW,MAAM,GAAC,MAAM,GAAC,UAAU,GAGvB,MAAM,GAAC,SAAS,CAW3B;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,iBAZW,OAAO,GAAC,MAAM,GAAC,OAAO,GAAC,KAAK,GAQ3B,OAAO,GAAC,MAAM,GAAC,SAAS,CAqCnC;IAED;;;;;;;;;;;;;;;OAeG;IACH,oBAZW,OAAO,GAMN,MAAM,GAAC,SAAS,CAY3B;IAED;;;;;;;;;;OAUG;IACH,aARW,OAAO,GAIN,OAAO,GAAC,SAAS,CAW5B;IAED;;;;;;;;;;;OAWG;IACH,aAPW,MAAM,GAGL,MAAM,GAAC,SAAS,CAmC3B;IAED;;;;;;;;;;;OAWG;IACH,gCAYC;IAED;;;;;;;;;;;;OAYG;IACH,gBARW,OAAO,GAIN,OAAO,GAAC,SAAS,CA6C5B;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,2BARW,OAAO,GAIN,OAAO,GAAC,SAAS,CAuC5B;IAvBC,0BAAgC;IAyBlC;;;;;;;;;;;;OAYG;IACH,YARY,UAAU,GAAC,MAAM,GAAC,MAAM,GAIxB,UAAU,GAAC,IAAI,GAAC,SAAS,CAwEpC;IA3BG,mBAAkB;IA6BtB;;;;;OAKG;IACH,qCAEC;IADC,uBAAyB;IAG3B;;;;;;;;;;;;;OAaG;IACH,kBARW,OAAO,GAIN,OAAO,GAAC,SAAS,CAoD5B;IAED;;;;OAIG;IACH,+BA2GC;IAED;;;;;;;;;;;;;OAaG;IACH,oBAPW,MAAM,GAGL,MAAM,GAAC,SAAS,CAgB3B;IAED;;;;;;;;;;;;;;OAcG;IACH,2BAPW,MAAM,GAGL,MAAM,GAAC,SAAS,CAa3B;IAED;;;;;;;;;;OAUG;IACH,eARW,OAAO,GAIN,OAAO,GAAC,SAAS,CAW5B;IALG,kBAAsB;IAO1B,2CASC;IAED,2BA8BC;IAED,4BAUC;IAED;;;;;;;;;;;;OAYG;IACH,sBAPW,OAAO,GAGN,eAAQ,OAAO,CAiC1B;IAED,4BAOC;IAED,6BAOC;IAED;;;;;;;;;OASG;IACH,wBAPW,OAAO,GAGN,eAAQ,OAAO,CAiC1B;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,oBAbW,MAAM,UAGN,MAAM,aAGN,MAAM,GAGL,SAAS,GAAC,SAAS,CAQ9B;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,iDAVW,OAAO,GAIN,gBAAgB,CAU3B;IAED;;;;;;;;;OASG;IACH,iCAHY,SAAS,CAgBpB;IAED;;;;;;;;;OASG;IACH,2BAJY,MAAO,SAAS,CAM3B;IAED;;;;;OAKG;IACH,cAHY,MAAM,CAKjB;IAED;;;;;OAKG;IACH,eAHY,MAAM,CAKjB;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,gBAPW,MAAM,GAGL,MAAM,GAAC,SAAS,CAuB3B;IAbG,kBAA2C;IAe/C;;;;;;;OAOG;IACH,mBAEC;IAED;;;;;;OAMG;IACH,cAgBC;IAED;;;;;;;;;;;;;;;OAeG;IACH,qBAXW,MAAM,cAAU,OAAO,WAAO,IAAI,kBAQjC,WAAW,CAgBtB;IAED;;;;OAIG;IACH,iCA6BC;IAJK,oBAAsC;IAM5C;;;;OAIG;IACH,iCAQC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmCG;IACH,0BA9BY,MAAO,OAAO,OA8CzB;IARC,kBAAuE;IAUzE;;;;;;;;;;;;OAYG;IACH,mBATY,OAAO,GAIP,OAAO,GAAC,SAAS,CAoC5B;IAfC,iBAAwB;IAiB1B;;;;;;OAMG;IACH,qBAJY,MAAM,CAMjB;IAED;;;;;;;OAOG;IACH,0BALY,MAAM,CAOjB;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2CG;IAEH;;;;;;;;OAQG;IACH,6CA8CC;IAED;;;;;;;OAOG;IACH,YAFY,MAAM,CA2BjB;IA6DD;;;;;;;OAOG;IACH,eALW,OAAO,GAGN,OAAO,GAAC,SAAS,CAkB5B;IAVG,uBAAuC;IAY3C;;;;;;;;;;;OAWG;IACH,yBALW,MAAM,EAAE,GAGP,MAAM,EAAE,CA0BnB;IAED;;;;;;;;;;;;OAYG;IAEH,gBAPY,MAAM,GAGN,OAAO,CAMlB;IAGD;;;;;;;;;;;;OAYG;IAEH,kBAPY,MAAM,GAGN,OAAO,CAMlB;IAIH;;;;;;;;;OASG;IACH,eALY,cAAc,CAKa;IAEvC;;;;;;;;;OASG;IACH,eALY,cAAc,CAKa;IAEvC;;;;;;;;;OASG;IACH,cALY,aAAa,CAKa;IAEtC;;;;;;;OAOG;IACH,oBALY,aAAa,CAKmB;IAE5C;;;;;;;OAOG;IACH,sBALY,oBAAoB,CAKc;IAiB9C;;;;;;;;;;;;;;OAcG;IACH,sBAtgJa,MAAM,GAAC,IAAI,KAIV,MAAM,GAAC,IAAI,GAAC,SAAS,CAkgJP;IAsB5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAAyB;CA9GxB;;;;sBA9xKqB,gBAAgB;8BAgCR,yBAAyB;iBARtC,gBAAgB;+BAkCF,cAAc;uBAvCtB,kBAAkB;kCAoCP,6BAA6B;wBAhCvC,gBAAgB;gCAoCR,2BAA2B;gCAL3B,2BAA2B;+BAG5B,0BAA0B;sCADnB,kCAAkC"}