export default CustomControlSpacer;
/**
 * Spacer specifically meant to be used as an insertion point for new plugins, etc.
 *
 * @extends Spacer
 */
declare class CustomControlSpacer extends Spacer {
    /**
     * Create the `Component`'s DOM element
     *
     * @return {Element}
     *         The element that was created.
     */
    createEl(): Element;
}
import Spacer from './spacer.js';
//# sourceMappingURL=custom-control-spacer.d.ts.map