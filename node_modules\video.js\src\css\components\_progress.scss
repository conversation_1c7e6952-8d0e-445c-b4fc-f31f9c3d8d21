// .vjs-progress-control / ProgressControl
//
// This is the container for all progress bar-related components/elements.
.video-js .vjs-progress-control {
  cursor: pointer;
  @include flex(auto);
  @include display-flex(center);
  min-width: 4em;
  touch-action: none;
}

.video-js .vjs-progress-control.disabled {
  cursor: default;
}

.vjs-live .vjs-progress-control {
  display: none;
}

.vjs-liveui .vjs-progress-control {
  @include display-flex(center);
}

// .vjs-progress-holder / SeekBar
//
// Box containing play and load progress bars. It also acts as seek scrubber.
.video-js .vjs-progress-holder {
  @include flex(auto);
  @include transition(all 0.2s);
  height: 0.3em;
}

.video-js .vjs-progress-control .vjs-progress-holder {

  // This is one of the rare cases where we are using a pixel dimension. The
  // reason is that the progress holder font-size changes on hover. With the
  // default em-based margins, this means it gets narrower and causes issues
  // with mouseover behaviors/math.
  margin: 0 10px;
}

// This increases the size of the progress holder so there is an increased
// hit area for clicks/touches.
.video-js .vjs-progress-control:hover .vjs-progress-holder,
.video-js.vjs-scrubbing.vjs-touch-enabled .vjs-progress-control .vjs-progress-holder {
  font-size: 1.666666666666666666em;
}

.video-js .vjs-progress-control:hover .vjs-progress-holder.disabled {
  font-size: 1em;
}

// .vjs-play-progress / PlayProgressBar and .vjs-load-progress / LoadProgressBar
//
// These are bars that appear within the progress control to communicate the
// amount of media that has played back and the amount of media that has
// loaded, respectively.
.video-js .vjs-progress-holder .vjs-play-progress,
.video-js .vjs-progress-holder .vjs-load-progress,
.video-js .vjs-progress-holder .vjs-load-progress div {
  position: absolute;
  display: block;
  height: 100%;
  margin: 0;
  padding: 0;
  // updated by javascript during playback
  width: 0;
}

.video-js .vjs-play-progress {
  background-color: $primary-foreground-color;
  @extend .vjs-icon-circle;

  // Progress handle
  &:before {
    font-size: 0.9em;
    position: absolute;
    right: -0.5em;
    line-height: .35em;
    z-index: 1;
  }
}

// Remove content from play-progress when using SVGs.
.vjs-svg-icons-enabled .vjs-play-progress {
  &:before {
    content: none !important;
  }
}

.vjs-play-progress .vjs-svg-icon {
  position: absolute;
  top: -0.35em;
  right: -0.4em;
  width: 0.9em;
  height: 0.9em;
  pointer-events: none;
  line-height: 0.15em;
  z-index: 1;
}

.video-js .vjs-load-progress {
  background: rgba($secondary-background-color, $secondary-background-transparency);
}

// There are child elements of the load progress bar that represent the
// specific time ranges that have been buffered.
.video-js .vjs-load-progress div {
  background: rgba($secondary-background-color, 0.75);
}

// .vjs-time-tooltip
//
// These elements are displayed above the progress bar.
//
// By default, they are hidden and only shown when hovering over the progress
// control.
.video-js .vjs-time-tooltip {
  @include background-color-with-alpha(#fff, 0.8);
  @include border-radius(0.3em);
  color: #000;

  // By floating the tooltips to the right, their right edge becomes aligned
  // with the right edge of their parent element. However, in order to have them
  // centered, they must be pulled further to the right via positioning (e.g.
  // `right: -10px;`. This part is left to JavaScript.
  float: right;
  font-family: $text-font-family;

  // The font-size should translate to a consistent 10px for time tooltips in
  // all states. This is tricky because the .vjs-progress-holder element
  // changes its font-size when the .vjs-progress-control is hovered.
  font-size: 1em;
  padding: 6px 8px 8px 8px;
  pointer-events: none;
  position: absolute;
  top: -3.4em;
  visibility: hidden;
  z-index: 1;
}

.video-js .vjs-progress-holder:focus .vjs-time-tooltip {
  display: none;
}

.video-js .vjs-progress-control:hover .vjs-time-tooltip,
.video-js .vjs-progress-control:hover .vjs-progress-holder:focus .vjs-time-tooltip,
.video-js.vjs-scrubbing.vjs-touch-enabled .vjs-progress-control .vjs-time-tooltip {
  display: block;

  // Ensure that we maintain a font-size of ~10px.
  font-size: 0.6em;
  visibility: visible;
}

.video-js .vjs-progress-control.disabled:hover .vjs-time-tooltip {
  font-size: 1em;
}

// .vjs-mouse-display / MouseTimeDisplay
//
// This element tracks the mouse position along the progress control and
// includes a tooltip, which displays the time at that point in the media.
.video-js .vjs-progress-control .vjs-mouse-display {
  display: none;
  position: absolute;
  width: 1px;
  height: 100%;
  background-color: #000;
  z-index: 1;
}

.video-js .vjs-progress-control:hover .vjs-mouse-display {
  display: block;
}

.video-js.vjs-scrubbing.vjs-touch-enabled .vjs-progress-control .vjs-mouse-display {
  display: block;
}

.video-js.vjs-user-inactive .vjs-progress-control .vjs-mouse-display,
.video-js.vjs-touch-enabled:not(.vjs-scrubbing) .vjs-progress-control .vjs-mouse-display {
  visibility: hidden;
  opacity: 0;
  $trans: visibility 1.0s, opacity 1.0s;
  @include transition($trans);
}

.vjs-mouse-display .vjs-time-tooltip {
  color: #fff;
  @include background-color-with-alpha(#000, 0.8);
}
