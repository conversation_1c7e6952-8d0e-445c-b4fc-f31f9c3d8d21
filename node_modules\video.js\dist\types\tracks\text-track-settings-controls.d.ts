export default TrackSettingsControls;
/**
 * Buttons of reset & done that modal 'TextTrackSettings'
 * uses as part of its content.
 *
 * 'Reset': Resets all settings on 'TextTrackSettings'.
 * 'Done': Closes 'TextTrackSettings' modal.
 *
 * @extends Component
 */
declare class TrackSettingsControls extends Component {
    constructor(player: any, options?: {});
    /**
     * Create the `TrackSettingsControls`'s DOM element
     *
     * @return {Element}
     *         The DOM element that gets created.
     */
    createEl(): Element;
}
import Component from '../component';
//# sourceMappingURL=text-track-settings-controls.d.ts.map