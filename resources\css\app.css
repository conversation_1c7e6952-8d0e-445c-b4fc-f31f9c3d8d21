@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for AI Animation Maker */

/* Line clamp utility for text truncation */
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Progress bar animations */
.progress-bar {
    transition: width 0.3s ease-in-out;
}

/* Loading spinner */
.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.125rem 0.625rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-pending {
    background-color: #f3f4f6;
    color: #1f2937;
}

.status-processing {
    background-color: #fef3c7;
    color: #92400e;
}

.status-completed {
    background-color: #d1fae5;
    color: #065f46;
}

.status-failed {
    background-color: #fee2e2;
    color: #991b1b;
}

/* Video player styles */
.video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.video-container video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Form enhancements */
.form-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.form-input:focus {
    outline: none;
    --tw-ring-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    border-color: #6366f1;
}

.form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    resize: vertical;
}

.form-textarea:focus {
    outline: none;
    --tw-ring-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    border-color: #6366f1;
}

.form-select {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.form-select:focus {
    outline: none;
    --tw-ring-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    border-color: #6366f1;
}

/* Button styles */
.btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border: 1px solid transparent;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.05), 0 0 0 4px rgba(99, 102, 241, 0.1);
}

.btn-primary {
    background-color: #4f46e5;
    color: white;
}

.btn-primary:hover {
    background-color: #4338ca;
}

.btn-primary:focus {
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.05), 0 0 0 4px rgba(79, 70, 229, 0.1);
}

.btn-secondary {
    background-color: #4b5563;
    color: white;
}

.btn-secondary:hover {
    background-color: #374151;
}

.btn-secondary:focus {
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.05), 0 0 0 4px rgba(75, 85, 99, 0.1);
}

.btn-success {
    background-color: #059669;
    color: white;
}

.btn-success:hover {
    background-color: #047857;
}

.btn-success:focus {
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.05), 0 0 0 4px rgba(5, 150, 105, 0.1);
}

.btn-danger {
    background-color: #dc2626;
    color: white;
}

.btn-danger:hover {
    background-color: #b91c1c;
}

.btn-danger:focus {
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.05), 0 0 0 4px rgba(220, 38, 38, 0.1);
}

.btn-warning {
    background-color: #d97706;
    color: white;
}

.btn-warning:hover {
    background-color: #b45309;
}

.btn-warning:focus {
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.05), 0 0 0 4px rgba(217, 119, 6, 0.1);
}

/* Card styles */
.card {
    background-color: white;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
}

.card-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 1rem 1.5rem;
    background-color: #f9fafb;
    border-top: 1px solid #e5e7eb;
}

/* Alert styles */
.alert {
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    position: relative;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: #d1fae5;
    border: 1px solid #34d399;
    color: #065f46;
}

.alert-error {
    background-color: #fee2e2;
    border: 1px solid #f87171;
    color: #991b1b;
}

.alert-warning {
    background-color: #fef3c7;
    border: 1px solid #fbbf24;
    color: #92400e;
}

.alert-info {
    background-color: #dbeafe;
    border: 1px solid #60a5fa;
    color: #1e40af;
}

/* Scene timeline styles */
.scene-timeline {
    position: relative;
}

.scene-timeline::before {
    content: '';
    position: absolute;
    left: 20px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e5e7eb;
}

.scene-item {
    position: relative;
    padding-left: 50px;
    margin-bottom: 30px;
}

.scene-marker {
    position: absolute;
    left: 12px;
    top: 8px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid #e5e7eb;
    background: white;
}

.scene-marker.completed {
    background: #10b981;
    border-color: #10b981;
}

.scene-marker.processing {
    background: #f59e0b;
    border-color: #f59e0b;
}

.scene-marker.failed {
    background: #ef4444;
    border-color: #ef4444;
}

/* Character grid */
.character-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.character-card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

.character-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
    background: #f3f4f6;
}

/* Animation preview */
.animation-preview {
    position: relative;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

.animation-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.7));
    padding: 1rem;
    color: white;
}

/* Responsive utilities */
@media (max-width: 640px) {
    .character-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
    
    .scene-item {
        padding-left: 40px;
    }
    
    .scene-timeline::before {
        left: 15px;
    }
    
    .scene-marker {
        left: 7px;
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    .dark\:bg-gray-900 {
        background-color: #111827;
    }
    
    .dark\:text-white {
        color: #ffffff;
    }
    
    .dark\:border-gray-700 {
        border-color: #374151;
    }
}
