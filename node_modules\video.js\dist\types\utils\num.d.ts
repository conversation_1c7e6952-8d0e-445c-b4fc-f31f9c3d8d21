/**
 * @file num.js
 * @module num
 */
/**
 * Keep a number between a min and a max value
 *
 * @param {number} number
 *        The number to clamp
 *
 * @param {number} min
 *        The minimum value
 * @param {number} max
 *        The maximum value
 *
 * @return {number}
 *         the clamped number
 */
export function clamp(number: number, min: number, max: number): number;
//# sourceMappingURL=num.d.ts.map