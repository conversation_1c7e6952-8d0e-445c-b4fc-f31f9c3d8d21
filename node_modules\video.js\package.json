{"name": "video.js", "description": "An HTML5 video player that supports HLS and DASH with a common API and skin.", "version": "8.23.3", "main": "./dist/video.cjs.js", "module": "./dist/video.es.js", "style": "./dist/video-js.css", "types": "./dist/types/video.d.ts", "copyright": "Copyright Brightcove, Inc. <https://www.brightcove.com/>", "license": "Apache-2.0", "keywords": ["dash", "hls", "html5", "player", "video", "videojs"], "homepage": "https://videojs.com", "author": "<PERSON>", "scripts": {"sandbox": "node build/sandbox.js", "prestart": "npm-run-all sandbox", "start": "npm-run-all -p watch karma-server", "clean": "shx rm -rf ./dist ./test/dist ./docs/api ./lang/zh-<PERSON>*.json", "postclean": "shx mkdir -p ./dist/lang ./test/dist", "changelog": "conventional-changelog -p videojs -i CHANGELOG.md -s", "build": "npm-run-all build-dev minify copy zip", "build-dev": "npm-run-all clean lint-errors build:js build:css build:lang build:test build:types", "build:test": "npm-run-all build:test:*", "build:test:a11y": "shx cp sandbox/descriptions.html.example sandbox/descriptions.test-a11y.html", "build:test:browserify": "browserify test/require/browserify.js -o test/dist/browserify.js", "build:test:webpack": "webpack --hide-modules test/require/webpack.js test/dist/webpack.js", "copy": "npm-run-all copy:*", "copy:fonts": "shx cp -R node_modules/videojs-font/fonts dist/font", "copy:examples": "shx cp -R docs/examples dist/", "build:js": "rollup -c", "build:css": "npm-run-all build:css:*", "build:css:cdn": "sass --load-path=\"./\" --load-path=\"./node_modules/\" --no-source-map src/css/vjs-cdn.scss dist/alt/video-js-cdn.css", "build:types": "tsc", "postbuild:css:cdn": "postcss --verbose --config postcss.config.js -d dist/alt dist/alt/video-js-cdn.css", "build:css:default": "sass --load-path=\"./\" --load-path=\"./node_modules/\" --no-source-map src/css/vjs.scss dist/video-js.css", "postbuild:css:default": "postcss --verbose --config postcss.config.js -d dist/ dist/video-js.css", "prebuild:lang": "npm-run-all -s prebuild:lang:*", "prebuild:lang:chinese-s": "shx cp lang/zh-C<PERSON>.json lang/zh-Hans.json", "prebuild:lang:chinese-t": "shx cp lang/zh-TW.json lang/zh-Hant.json", "build:lang": "vjslang --dir dist/lang", "postbuild:lang": "shx cp -R lang/* dist/lang/", "minify": "npm-run-all minify:*", "minify:js": "node build/minify.js", "minify:css": "npm-run-all minify:css:*", "minify:css:cdn": "cleancss dist/alt/video-js-cdn.css -o dist/alt/video-js-cdn.min.css", "minify:css:default": "cleancss dist/video-js.css -o dist/video-js.min.css", "watch": "npm-run-all -p watch:*", "watch:lang": "chokidar --initial \"lang/**/!(zh-<PERSON>|zh-<PERSON><PERSON>)*.json\" -c \"npm run build:lang\"", "watch:rollup": "rollup -c -w --no-progress", "watch:types": "tsc -w", "watch:css": "npm-run-all -p build:css:default build:css:cdn watch:css:*", "watch:css:default": "npm run build:css:default -- --watch", "watch:css:cdn": "npm run build:css:cdn -- --watch", "assets": "node build/assets.js", "lint": "vjsstandard", "lint-errors": "vjsstandard --errors", "karma-server": "karma start test/karma.conf.js --singleRun=false --auto-watch", "posttest": "[ \"$CI_TEST_TYPE\" != \"coverage\" ] || shx cat test/dist/coverage/text.txt", "pretest": "npm run build-dev", "test": "npm-run-all -p test:*", "test:node-require": "node test/require/node.js", "test:a11y": "node build/test-a11y.js", "test:unit": "karma start test/karma.conf.js", "docs": "npm-run-all clean docs:lint docs:api", "docs:api": "jsdoc -c .jsdoc.js", "postdocs:api": "node ./build/fix-api-docs.js", "docs:lint": "remark -- \"./{,!(node_modules)/**/}!(CHANGELOG)*.md\"", "docs:fix": "remark --output -- \"./{,!(node_modules)/**/}!(CHANGELOG)*.md\"", "docs:lang": "node build/translations.js", "netlify": "node ./build/netlify.js", "netlify-docs": "node ./build/netlify-docs.js", "prepublishOnly": "run-p build", "version": "is-prerelease || npm run changelog && node build/readme-version.js && git add CHANGELOG.md README.md", "zip": "cd dist && cross-env bestzip \"./video-js-${npm_package_version}.zip\" * && cd .."}, "repository": {"type": "git", "url": "https://github.com/videojs/video.js.git"}, "dependencies": {"@babel/runtime": "^7.12.5", "@videojs/http-streaming": "^3.17.0", "@videojs/vhs-utils": "^4.1.1", "@videojs/xhr": "2.7.0", "aes-decrypter": "^4.0.2", "global": "4.4.0", "m3u8-parser": "^7.2.0", "mpd-parser": "^1.3.1", "mux.js": "^7.0.1", "videojs-contrib-quality-levels": "4.1.0", "videojs-font": "4.2.0", "videojs-vtt.js": "0.15.5"}, "devDependencies": {"@babel/core": "^7.9.0", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.9.0", "@rollup/plugin-image": "^3.0.2", "@rollup/plugin-replace": "^2.4.1", "@rollup/pluginutils": "^5.1.0", "@types/node": "^18.8.3", "access-sniff": "^3.2.0", "autoprefixer": "^10.2.5", "bestzip": "^2.2.0", "browserify": "^16.2.3", "chokidar-cli": "^2.1.0", "clean-css-cli": "^4.3.0", "clean-jsdoc-theme": "^4.2.1", "cli-table": "^0.3.1", "conventional-changelog-cli": "^2.0.21", "conventional-changelog-videojs": "^3.0.1", "cross-env": "^7.0.3", "es5-shim": "^4.5.15", "es6-shim": "^0.35.6", "filesize": "^4.1.2", "gh-release": "^6.0.4", "humanize-duration": "^3.26.0", "husky": "^1.3.1", "is-ci": "^3.0.0", "jsdoc": "^4.0.3", "karma": "^6.4.3", "lint-staged": "^10.5.4", "markdown-table": "^1.1.3", "maxmin": "^2.1.0", "not-prerelease": "^1.0.1", "npm-merge-driver-install": "^2.0.1", "npm-run-all": "^4.1.5", "postcss": "^8.2.13", "postcss-cli": "^8.3.1", "qunit": "2.13.0", "remark-cli": "^6.0.1", "remark-lint": "^6.0.6", "remark-parse": "^6.0.3", "remark-stringify": "^6.0.4", "remark-toc": "^5.1.1", "remark-validate-links": "^8.0.2", "replace": "^1.2.1", "rollup": "^2.2.0", "rollup-plugin-alias": "^1.5.2", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-commonjs": "^9.3.4", "rollup-plugin-external-globals": "^0.6.1", "rollup-plugin-ignore": "^1.0.5", "rollup-plugin-istanbul": "^3.0.0", "rollup-plugin-json": "^3.1.0", "rollup-plugin-multi-entry": "^2.0.2", "rollup-plugin-node-resolve": "^4.2.4", "rollup-plugin-progress": "^1.1.2", "rollup-plugin-stub": "^1.2.0", "rollup-plugin-svg": "^2.0.0", "sass": "^1.79.3", "semver": "^5.7.0", "shelljs": "^0.8.5", "shx": "^0.3.2", "sinon": "^11.1.1", "typescript": "^5.5.2", "uglify-js": "^3.19.0", "unified": "^7.0.2", "videojs-generate-karma-config": "^8.1.0", "videojs-languages": "^2.0.0", "videojs-standard": "^9.0.1", "webpack": "^1.15.0"}, "vjsstandard": {"ignore": ["dist", "docs", "sandbox", "test/dist", "test/api", "core.js", "core.es.js"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["vjsstandard --fix"], "!(CHANGELOG)*.md": ["remark --output --"], "lang/**/!(zh-Hans|zh-Hant)*.json": ["node build/translations.js"]}}