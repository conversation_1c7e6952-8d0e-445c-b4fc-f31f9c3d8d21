export default checkMuteSupport;
/** @import Component from '../../component' */
/** @import Player from '../../player' */
/**
 * Check if muting volume is supported and if it isn't hide the mute toggle
 * button.
 *
 * @param {Component} self
 *        A reference to the mute toggle button
 *
 * @param {Player} player
 *        A reference to the player
 *
 * @private
 */
declare function checkMuteSupport(self: Component, player: Player): void;
import type Component from '../../component';
import type Player from '../../player';
//# sourceMappingURL=check-mute-support.d.ts.map