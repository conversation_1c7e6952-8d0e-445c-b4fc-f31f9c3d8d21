declare namespace _default {
    let NetworkBadStatus: string;
    let NetworkRequestFailed: string;
    let NetworkRequestAborted: string;
    let NetworkRequestTimeout: string;
    let NetworkBodyParserFailed: string;
    let StreamingHlsPlaylistParserError: string;
    let StreamingDashManifestParserError: string;
    let StreamingContentSteeringParserError: string;
    let StreamingVttParserError: string;
    let StreamingFailedToSelectNextSegment: string;
    let StreamingFailedToDecryptSegment: string;
    let StreamingFailedToTransmuxSegment: string;
    let StreamingFailedToAppendSegment: string;
    let StreamingCodecsChangeError: string;
}
export default _default;
//# sourceMappingURL=errors.d.ts.map