export default Spacer;
/**
 * Just an empty spacer element that can be used as an append point for plugins, etc.
 * Also can be used to create space between elements when necessary.
 *
 * @extends Component
 */
declare class Spacer extends Component {
    /**
     * Create the `Component`'s DOM element
     *
     * @return {Element}
     *         The element that was created.
     */
    createEl(tag?: string, props?: {}, attributes?: {}): Element;
}
import Component from '../../component.js';
//# sourceMappingURL=spacer.d.ts.map